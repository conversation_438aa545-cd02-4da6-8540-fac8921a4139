package ai.showlab.bff.common.filter;

import ai.showlab.bff.common.exception.BusinessException;
import ai.showlab.bff.common.security.token.JwtTokenProvider;
import ai.showlab.bff.entity.bo.LoginMember;
import ai.showlab.common.core.constant.CacheConstants;
import ai.showlab.common.core.constant.CodeConstants;
import ai.showlab.common.core.web.domain.RestResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * JWT 认证过滤器
 * 负责从请求中提取 JWT Token，验证并设置认证信息到 Spring Security 上下文。
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtTokenProvider jwtTokenProvider;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        try {
            String jwt = getJwtFromRequest(request);
            if (StringUtils.hasText(jwt)) {
                // 检查 Token 是否在黑名单中
                if (redisTemplate.hasKey(CacheConstants.BFF_TOKEN_BLACKLIST_KEY + ":" + jwt)) {
                    log.warn("拒绝访问: Token 已在黑名单中。Token: {}", jwt);
                    writeUnauthorizedResponse(response, "登录认证信息失效，请重新登录");
                    return; // 终止请求处理
                }

                try {
                    if (jwtTokenProvider.validateToken(jwt)) {
                        Claims claims = jwtTokenProvider.getClaimsFromToken(jwt);
                        Long memberId = Long.parseLong(claims.getSubject());
                        // 假设 username 也在 claims 中
                        String username = (String) claims.get("username");

                        LoginMember loginMember = new LoginMember(memberId, username, jwt, "member");

                        UsernamePasswordAuthenticationToken authentication =
                                new UsernamePasswordAuthenticationToken(loginMember, null, null);
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                        SecurityContextHolder.getContext().setAuthentication(authentication);
                    }
                } catch (BusinessException e) {
                    // JWT 验证失败（过期、签名错误等）
                    log.warn("拒绝访问: JWT 验证失败 - {}", e.getMessage());
                    writeUnauthorizedResponse(response, e.getMessage());
                    return; // 终止请求处理
                }
            }
        } catch (Exception ex) {
            log.error("处理 JWT 认证时发生内部错误", ex);
            writeServerErrorResponse(response, "系统繁忙，请稍后重试");
            return; // 终止请求处理
        }

        filterChain.doFilter(request, response);
    }

    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 写入401未授权响应
     */
    private void writeUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(CodeConstants.UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        RestResult result = RestResult.error(HttpServletResponse.SC_UNAUTHORIZED, message);
        String jsonResponse = objectMapper.writeValueAsString(result);
        response.getWriter().write(jsonResponse);
    }

    /**
     * 写入500服务器错误响应
     */
    private void writeServerErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(CodeConstants.SERVER_ERROR);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        RestResult result = RestResult.error(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, message);
        String jsonResponse = objectMapper.writeValueAsString(result);
        response.getWriter().write(jsonResponse);
    }
} 