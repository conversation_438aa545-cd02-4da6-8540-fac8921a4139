<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.showlab.bff.mapper.v1.billing.BillingPackageMapper">

    <resultMap type="ai.showlab.bff.entity.domain.v1.billing.BillingPackage" id="BillingPackageResult">
        <result property="id" column="id"/>
        <result property="ownerMemberId" column="owner_member_id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="type" column="type"/>
        <result property="price" column="price"/>
        <result property="currencyId" column="currency_id"/>
        <result property="creditsGranted" column="credits_granted"/>
        <result property="validityDays" column="validity_days"/>
        <result property="renewalIntervalUnit" column="renewal_interval_unit"/>
        <result property="memberLevelGrant" column="member_level_grant"/>
        <result property="status" column="status"/>
        <result property="sortOrder" column="sort_order"/>
    </resultMap>

    <sql id="selectBillingPackageVo">
        select id, owner_member_id, code, name, description, type, price, currency_id, credits_granted,
        validity_days, renewal_interval_unit, member_level_grant, status, sort_order
        from a_billing_package
    </sql>

    <select id="selectAvailablePackages" resultMap="BillingPackageResult">
        <include refid="selectBillingPackageVo"/>
        where owner_member_id is null
        and status = 2 -- 2: 上架
        and delete_time is null
        order by sort_order asc
    </select>

    <select id="selectPackageById" parameterType="Long" resultMap="BillingPackageResult">
        <include refid="selectBillingPackageVo"/>
        where id = #{id}
        and status = 2 -- 2: 上架
        and delete_time is null
    </select>

    <select id="selectPackagesByCondition" parameterType="ai.showlab.bff.entity.param.BillingPackageListParam" resultMap="BillingPackageResult">
        <include refid="selectBillingPackageVo"/>
        <where>
            and owner_member_id is null
            and status = 2 -- 2: 上架
            and delete_time is null
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="currencyId != null">
                and currency_id = #{currencyId}
            </if>
            <if test="minPrice != null">
                and price >= #{minPrice}
            </if>
            <if test="maxPrice != null">
                and price &lt;= #{maxPrice}
            </if>
            <if test="keyword != null and keyword != ''">
                and (name like concat('%', #{keyword}, '%') or description like concat('%', #{keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="sortBy == 'price'">
                order by price
                <if test="sortOrder == 'desc'">desc</if>
                <if test="sortOrder != 'desc'">asc</if>
            </when>
            <when test="sortBy == 'credits_granted'">
                order by credits_granted
                <if test="sortOrder == 'desc'">desc</if>
                <if test="sortOrder != 'desc'">asc</if>
            </when>
            <otherwise>
                order by sort_order
                <if test="sortOrder == 'desc'">desc</if>
                <if test="sortOrder != 'desc'">asc</if>
            </otherwise>
        </choose>
    </select>

    <select id="countPackagesByCondition" parameterType="ai.showlab.bff.entity.param.BillingPackageListParam" resultType="long">
        select count(*)
        from a_billing_package
        <where>
            and owner_member_id is null
            and status = 2 -- 2: 上架
            and delete_time is null
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="currencyId != null">
                and currency_id = #{currencyId}
            </if>
            <if test="minPrice != null">
                and price >= #{minPrice}
            </if>
            <if test="maxPrice != null">
                and price &lt;= #{maxPrice}
            </if>
            <if test="keyword != null and keyword != ''">
                and (name like concat('%', #{keyword}, '%') or description like concat('%', #{keyword}, '%'))
            </if>
        </where>
    </select>

    <!-- 直接查询套餐VO，包含关联的货币信息和字典描述 -->
    <resultMap type="ai.showlab.bff.entity.vo.v1.BillingPackageVo" id="BillingPackageVoResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="type" column="type"/>
        <result property="typeDesc" column="type_desc"/>
        <result property="price" column="price"/>
        <result property="currencySymbol" column="currency_symbol"/>
        <result property="currencyCode" column="currency_code"/>
        <result property="creditsGranted" column="credits_granted"/>
        <result property="validityDays" column="validity_days"/>
        <result property="validityDesc" column="validity_desc"/>
        <result property="renewalIntervalUnit" column="renewal_interval_unit"/>
        <result property="renewalIntervalDesc" column="renewal_interval_desc"/>
        <result property="memberLevelGrant" column="member_level_grant"/>
        <result property="memberLevelDesc" column="member_level_desc"/>
        <result property="isRecommended" column="is_recommended"/>
        <result property="isPopular" column="is_popular"/>
    </resultMap>

    <select id="selectPackageVosByCondition" parameterType="ai.showlab.bff.entity.param.BillingPackageListParam" resultMap="BillingPackageVoResult">
        select
        p.id,
        p.code,
        p.name,
        p.description,
        p.type,
        COALESCE(pt.dict_label, '未知类型') as type_desc,
        p.price,
        COALESCE(c.symbol, '¥') as currency_symbol,
        COALESCE(c.code, 'CNY') as currency_code,
        p.credits_granted,
        p.validity_days,
        CASE
        WHEN p.validity_days IS NULL OR p.validity_days &lt;= 0 THEN '永久有效'
        WHEN p.validity_days = 30 THEN '30天有效'
        WHEN p.validity_days = 365 THEN '1年有效'
        WHEN p.validity_days % 30 = 0 THEN CONCAT(p.validity_days / 30, '个月有效')
        ELSE CONCAT(p.validity_days, '天有效')
        END as validity_desc,
        p.renewal_interval_unit,
        COALESCE(ri.dict_label, '一次性') as renewal_interval_desc,
        p.member_level_grant,
        COALESCE(ml.dict_label, '普通会员') as member_level_desc,
        FALSE as is_recommended,
        FALSE as is_popular
        from a_billing_package p
        left join a_base_currency c on p.currency_id = c.id and c.delete_time is null
        left join sys_dict_data pt on pt.dict_type = 'package_type' and pt.dict_value = CAST(p.type as VARCHAR) and pt.status = '0'
        left join sys_dict_data ri on ri.dict_type = 'renewal_interval_unit' and ri.dict_value = CAST(p.renewal_interval_unit as VARCHAR) and ri.status = '0'
        left join sys_dict_data ml on ml.dict_type = 'member_level' and ml.dict_value = CAST(p.member_level_grant as VARCHAR) and ml.status = '0'
        <where>
            and p.owner_member_id is null
            and p.status = 2 -- 2: 上架
            and p.delete_time is null
            <if test="type != null">
                and p.type = #{type}
            </if>
            <if test="currencyId != null">
                and p.currency_id = #{currencyId}
            </if>
            <if test="minPrice != null">
                and p.price >= #{minPrice}
            </if>
            <if test="maxPrice != null">
                and p.price &lt;= #{maxPrice}
            </if>
            <if test="keyword != null and keyword != ''">
                and (p.name like concat('%', #{keyword}, '%') or p.description like concat('%', #{keyword}, '%'))
            </if>
        </where>
        <choose>
            <when test="sortBy == 'price'">
                order by p.price
                <if test="sortOrder == 'desc'">desc</if>
                <if test="sortOrder != 'desc'">asc</if>
            </when>
            <when test="sortBy == 'credits_granted'">
                order by p.credits_granted
                <if test="sortOrder == 'desc'">desc</if>
                <if test="sortOrder != 'desc'">asc</if>
            </when>
            <otherwise>
                order by p.sort_order
                <if test="sortOrder == 'desc'">desc</if>
                <if test="sortOrder != 'desc'">asc</if>
            </otherwise>
        </choose>
    </select>

</mapper>